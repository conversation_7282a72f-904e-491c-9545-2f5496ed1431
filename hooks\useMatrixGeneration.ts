'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { generateMatrixId } from '@/utils/matrixUtils';

interface FormData {
  mainKeyword: string;
  location: string;
  language: string;
}

interface Project {
  _id?: string;
  id?: string;
  name: string;
}

interface MatrixGenerationState {
  isLoading: boolean;
  showLoadingPage: boolean;
  error: string | null;
  generatedMatrix: any | null;
}

interface UseMatrixGenerationReturn {
  state: MatrixGenerationState;
  generateMatrix: (formData: FormData, project: Project) => Promise<void>;
  resetState: () => void;
}

export const useMatrixGeneration = (): UseMatrixGenerationReturn => {
  const router = useRouter();
  const { user } = useAuth();
  
  const [state, setState] = useState<MatrixGenerationState>({
    isLoading: false,
    showLoadingPage: false,
    error: null,
    generatedMatrix: null
  });

  const resetState = useCallback(() => {
    setState({
      isLoading: false,
      showLoadingPage: false,
      error: null,
      generatedMatrix: null
    });
  }, []);

  const generateMatrix = useCallback(async (formData: FormData, project: Project) => {
    if (!user || !project) {
      setState(prev => ({ ...prev, error: 'User or project not found' }));
      return;
    }

    // Prevent duplicate submissions
    if (state.isLoading) {
      console.log('🚫 Matrix generation blocked - already processing');
      return;
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      showLoadingPage: true,
      error: null,
      generatedMatrix: null
    }));

    try {
      console.log('🚀 Starting matrix generation process...');

      // Prepare webhook data
      const webhookData = {
        userId: user.id,
        plan: user.plan || 'free',
        mainKeyword: formData.mainKeyword,
        location: formData.location,
        language: formData.language,
      };

      // Create webhook promise for the loading page
      const webhookPromise = createWebhookPromise(webhookData, formData, project);

      // The loading page will handle the webhook completion
      // We just need to set up the promise and let the loading page manage it
      
    } catch (err) {
      console.error('Error starting matrix generation:', err);
      setState(prev => ({
        ...prev,
        isLoading: false,
        showLoadingPage: false,
        error: 'Failed to start matrix generation. Please try again.'
      }));
    }
  }, [user, state.isLoading]);

  const createWebhookPromise = async (
    webhookData: any,
    formData: FormData,
    project: Project
  ): Promise<any> => {
    // Import webhook function dynamically
    const { sendMatrixGenerationToWebhook } = await import('@/utils/webhookUtils');

    let webhookResponse = null;
    try {
      webhookResponse = await sendMatrixGenerationToWebhook(webhookData);
      console.log('✅ Webhook response received:', webhookResponse);
    } catch (webhookError) {
      console.warn('⚠️ Webhook call failed, continuing with local processing:', webhookError);
    }

    // Process webhook data with fallback to mock data
    const { processWebhookData } = await import('@/utils/dataProcessingUtils');
    const processedData = processWebhookData(
      webhookResponse,
      formData.mainKeyword,
      formData.location,
      formData.language
    );

    console.log('📊 Data processing result:', processedData.summary);

    let matrix: any;

    try {
      // Try to create matrix via API
      const response = await fetch('/api/matrices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          projectId: project._id || project.id,
          mainKeyword: formData.mainKeyword,
          location: formData.location,
          language: formData.language,
          keywordResearch: processedData.keywordResearch,
          contentMatrix: processedData.contentMatrix,
        }),
      });

      if (!response.ok) {
        throw new Error('API call failed');
      }

      const data = await response.json();
      matrix = data.matrix;

      console.log('✅ Matrix created via API:', matrix);
    } catch (apiError) {
      console.warn('⚠️ API failed, using fallback approach:', apiError);

      // Fallback: Create matrix with keyword-based ID
      const matrixId = generateMatrixId(formData.mainKeyword);

      matrix = {
        _id: matrixId,
        projectId: project._id || project.id,
        userId: user?.id,
        mainKeyword: formData.mainKeyword,
        filename: `${matrixId}-matrix.json`,
        location: formData.location,
        language: formData.language,
        keywordResearch: processedData.keywordResearch,
        contentMatrix: processedData.contentMatrix,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      console.log('🔄 Using fallback matrix:', matrix);
    }

    return matrix;
  };

  const handleMatrixComplete = useCallback((matrixId: string, project: Project) => {
    console.log('✅ Matrix generation completed:', matrixId);
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      showLoadingPage: false
    }));

    // Create project slug for routing
    const projectSlug = project.name.toLowerCase().replace(/\s+/g, '-');
    const redirectUrl = `/project/${projectSlug}/matrix/${matrixId}`;

    console.log('🔄 Redirecting to matrix results:', redirectUrl);

    // Redirect to the matrix results page
    setTimeout(() => {
      router.push(redirectUrl);
    }, 1000);
  }, [router]);

  const handleMatrixError = useCallback((error: string) => {
    console.error('❌ Matrix generation failed:', error);
    
    setState(prev => ({
      ...prev,
      isLoading: false,
      showLoadingPage: false,
      error
    }));
  }, []);

  return {
    state,
    generateMatrix,
    resetState,
    handleMatrixComplete,
    handleMatrixError,
    createWebhookPromise
  } as UseMatrixGenerationReturn & {
    handleMatrixComplete: (matrixId: string, project: Project) => void;
    handleMatrixError: (error: string) => void;
    createWebhookPromise: (webhookData: any, formData: FormData, project: Project) => Promise<any>;
  };
};
