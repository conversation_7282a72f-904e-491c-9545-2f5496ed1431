'use client';

/**
 * Loading State Manager
 * Manages the loading states and progress for matrix generation
 */

export interface LoadingState {
  isLoading: boolean;
  currentStep: number;
  totalSteps: number;
  currentMessage: string;
  timeRemaining: number; // in seconds
  progress: number; // percentage 0-100
}

export interface LoadingStep {
  id: number;
  message: string;
  duration: number; // in seconds
  description?: string;
}

// Default loading steps for matrix generation
export const MATRIX_GENERATION_STEPS: LoadingStep[] = [
  {
    id: 1,
    message: "Initializing keyword analysis...",
    duration: 15,
    description: "Setting up the analysis environment"
  },
  {
    id: 2,
    message: "Analyzing keyword semantics...",
    duration: 25,
    description: "Understanding keyword context and intent"
  },
  {
    id: 3,
    message: "Researching competitor content...",
    duration: 20,
    description: "Analyzing top-ranking content for insights"
  },
  {
    id: 4,
    message: "Generating content clusters...",
    duration: 30,
    description: "Creating thematic content groupings"
  },
  {
    id: 5,
    message: "Building keyword matrix...",
    duration: 20,
    description: "Organizing keywords by search intent"
  },
  {
    id: 6,
    message: "Finalizing content strategy...",
    duration: 10,
    description: "Optimizing recommendations"
  }
];

export class LoadingStateManager {
  private state: LoadingState;
  private steps: LoadingStep[];
  private intervalId: NodeJS.Timeout | null = null;
  private onStateChange: (state: LoadingState) => void;
  private startTime: number = 0;

  constructor(
    steps: LoadingStep[] = MATRIX_GENERATION_STEPS,
    onStateChange: (state: LoadingState) => void
  ) {
    this.steps = steps;
    this.onStateChange = onStateChange;
    
    const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0);
    
    this.state = {
      isLoading: false,
      currentStep: 0,
      totalSteps: steps.length,
      currentMessage: "Preparing to generate content matrix...",
      timeRemaining: totalDuration,
      progress: 0
    };
  }

  // Start the loading process
  start(): void {
    if (this.state.isLoading) {
      console.warn('Loading already in progress');
      return;
    }

    this.startTime = Date.now();
    this.state = {
      ...this.state,
      isLoading: true,
      currentStep: 1,
      currentMessage: this.steps[0]?.message || "Starting...",
      timeRemaining: this.getTotalDuration(),
      progress: 0
    };

    this.onStateChange(this.state);
    this.startTimer();
  }

  // Stop the loading process
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    this.state = {
      ...this.state,
      isLoading: false,
      progress: 100,
      currentMessage: "Content matrix generated successfully!",
      timeRemaining: 0
    };

    this.onStateChange(this.state);
  }

  // Get current state
  getState(): LoadingState {
    return { ...this.state };
  }

  // Get total duration of all steps
  private getTotalDuration(): number {
    return this.steps.reduce((sum, step) => sum + step.duration, 0);
  }

  // Start the timer that updates progress
  private startTimer(): void {
    this.intervalId = setInterval(() => {
      this.updateProgress();
    }, 1000); // Update every second
  }

  // Update progress based on elapsed time
  private updateProgress(): void {
    const elapsedTime = (Date.now() - this.startTime) / 1000; // in seconds
    const totalDuration = this.getTotalDuration();
    
    // Calculate which step we should be on
    let cumulativeTime = 0;
    let currentStepIndex = 0;
    
    for (let i = 0; i < this.steps.length; i++) {
      cumulativeTime += this.steps[i].duration;
      if (elapsedTime <= cumulativeTime) {
        currentStepIndex = i;
        break;
      }
    }

    // If we've exceeded total time, stay on last step
    if (elapsedTime >= totalDuration) {
      currentStepIndex = this.steps.length - 1;
    }

    const currentStep = this.steps[currentStepIndex];
    const progress = Math.min((elapsedTime / totalDuration) * 100, 99); // Cap at 99% until completion
    const timeRemaining = Math.max(totalDuration - elapsedTime, 0);

    this.state = {
      ...this.state,
      currentStep: currentStepIndex + 1,
      currentMessage: currentStep?.message || "Processing...",
      progress: Math.round(progress),
      timeRemaining: Math.round(timeRemaining)
    };

    this.onStateChange(this.state);

    // Auto-stop if we've exceeded the total duration significantly
    if (elapsedTime > totalDuration + 30) { // 30 second buffer
      console.warn('Loading exceeded expected duration, auto-stopping');
      this.stop();
    }
  }

  // Format time remaining as MM:SS
  static formatTimeRemaining(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Get step description
  getCurrentStepDescription(): string {
    if (this.state.currentStep === 0) return '';
    const step = this.steps[this.state.currentStep - 1];
    return step?.description || '';
  }

  // Force update to specific step (for testing)
  setStep(stepNumber: number): void {
    if (stepNumber < 1 || stepNumber > this.steps.length) {
      console.warn('Invalid step number');
      return;
    }

    const step = this.steps[stepNumber - 1];
    this.state = {
      ...this.state,
      currentStep: stepNumber,
      currentMessage: step.message
    };

    this.onStateChange(this.state);
  }

  // Cleanup
  destroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}
