'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AppLayout from '@/components/AppLayout';
import KeywordForm from '@/components/KeywordForm';
import MatrixLoadingPage from '@/components/MatrixLoadingPage';
import { useAuth } from '@/context/AuthContext';
import { FormData, Project, Matrix } from '@/types';
import { generateMockWebhookData } from '@/utils/mockData';
import { generateMatrixId } from '@/utils/matrixUtils';

interface NewMatrixPageProps {
  params: {
    slug: string;
  };
}

export default function NewMatrixPage({ params }: NewMatrixPageProps) {
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showLoadingPage, setShowLoadingPage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [webhookPromise, setWebhookPromise] = useState<Promise<any> | null>(null);
  const [currentFormData, setCurrentFormData] = useState<FormData | null>(null);
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.push('/login-with-otp');
      return;
    }

    const fetchProject = async () => {
      try {
        const response = await fetch(`/api/projects/slug/${params.slug}`);
        if (!response.ok) {
          throw new Error('Failed to fetch project');
        }

        const { project: fetchedProject } = await response.json();
        setProject(fetchedProject);
      } catch (err) {
        console.error('Error fetching project:', err);
        setError('Failed to load project. Please try again.');
      }
    };

    fetchProject();
  }, [user, params.slug, router]);

  const createWebhookPromise = async (formData: FormData): Promise<any> => {
    if (!user || !project) throw new Error('User or project not found');

    console.log('🚀 Starting matrix generation process...');

    const webhookData = {
      userId: user.id,
      plan: user.plan || 'free',
      mainKeyword: formData.mainKeyword,
      location: formData.location,
      language: formData.language,
    };

    // Import the webhook function dynamically to avoid SSR issues
    const { sendMatrixGenerationToWebhook } = await import('@/utils/webhookUtils');

    let webhookResponse = null;
    try {
      webhookResponse = await sendMatrixGenerationToWebhook(webhookData);
      console.log('✅ Webhook response received:', webhookResponse);
    } catch (webhookError) {
      console.warn('⚠️ Webhook call failed, continuing with local processing:', webhookError);
    }

    // Process webhook data with fallback to mock data
    const { processWebhookData } = await import('@/utils/dataProcessingUtils');
    const processedData = processWebhookData(
      webhookResponse,
      formData.mainKeyword,
      formData.location,
      formData.language
    );

    console.log('📊 Data processing result:', processedData.summary);

    let matrix: any;

    try {
      // Try to create matrix via API
      const response = await fetch('/api/matrices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          projectId: project._id || project.id,
          mainKeyword: formData.mainKeyword,
          location: formData.location,
          language: formData.language,
          keywordResearch: processedData.keywordResearch,
          contentMatrix: processedData.contentMatrix,
        }),
      });

      if (!response.ok) {
        throw new Error('API call failed');
      }

      const data = await response.json();
      matrix = data.matrix;

      console.log('✅ Matrix created via API:', matrix);
    } catch (apiError) {
      console.warn('⚠️ API failed, using fallback approach:', apiError);

      // Fallback: Create matrix with keyword-based ID
      const matrixId = generateMatrixId(formData.mainKeyword);

      matrix = {
        _id: matrixId,
        projectId: project._id || project.id,
        userId: user.id,
        mainKeyword: formData.mainKeyword,
        filename: `${matrixId}-matrix.json`,
        location: formData.location,
        language: formData.language,
        keywordResearch: processedData.keywordResearch,
        contentMatrix: processedData.contentMatrix,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      console.log('🔄 Using fallback matrix:', matrix);
    }

    return matrix;
  };

  const handleFormSubmit = async (formData: FormData) => {
    if (!user || !project) return;

    // Prevent duplicate submissions
    if (isLoading || showLoadingPage) {
      console.log('🚫 Form submission blocked - already processing');
      return;
    }

    setIsLoading(true);
    setError(null);
    setCurrentFormData(formData);

    try {
      // Create the webhook promise
      const promise = createWebhookPromise(formData);
      setWebhookPromise(promise);

      // Show loading page
      setShowLoadingPage(true);
      setIsLoading(false); // Reset form loading state

    } catch (err) {
      console.error('💥 Error starting matrix generation:', err);
      setError(err instanceof Error ? err.message : 'Failed to start matrix generation. Please try again.');
      setIsLoading(false);
    }
  };

  const handleMatrixComplete = (matrixId: string) => {
    console.log('✅ Matrix generation completed:', matrixId);

    setShowLoadingPage(false);
    setWebhookPromise(null);
    setCurrentFormData(null);

    // Redirect to the new matrix page
    const projectSlug = project?.name.toLowerCase().replace(/\s+/g, '-');
    const redirectUrl = `/project/${projectSlug}/matrix/${matrixId}`;

    console.log('🔄 Redirecting to:', redirectUrl);

    // Add a small delay to ensure state updates
    setTimeout(() => {
      router.push(redirectUrl);
    }, 1000);
  };

  const handleMatrixError = (error: string) => {
    console.error('❌ Matrix generation failed:', error);

    setShowLoadingPage(false);
    setWebhookPromise(null);
    setCurrentFormData(null);
    setError(error);
  };

  if (!user) {
    return null; // Will redirect to login
  }

  // Show loading page during matrix generation
  if (showLoadingPage && currentFormData && project && webhookPromise) {
    return (
      <MatrixLoadingPage
        keyword={currentFormData.mainKeyword}
        location={currentFormData.location}
        language={currentFormData.language}
        projectName={project.name}
        projectSlug={project.name.toLowerCase().replace(/\s+/g, '-')}
        onComplete={handleMatrixComplete}
        onError={handleMatrixError}
        webhookPromise={webhookPromise}
      />
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          {error}
        </div>
      </AppLayout>
    );
  }

  if (!project) {
    return (
      <AppLayout>
        <div className="space-y-8">
          <div className="bg-white shadow-md rounded-lg p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded mb-4 w-1/3"></div>
              <div className="h-4 bg-gray-200 rounded mb-6 w-1/2"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Create New Matrix</h1>
              <p className="text-gray-600">
                Generate a new SEO Content Matrix for <strong>{project.name}</strong>
              </p>
            </div>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              ← Back
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
            {error}
          </div>
        )}

        {/* Matrix Creation Form */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Enter Keyword Information</h2>
          <p className="text-gray-600 mb-6">
            Enter your main keyword to generate a comprehensive content matrix.
          </p>

          <KeywordForm onSubmit={handleFormSubmit} isLoading={isLoading} />
        </div>
      </div>
    </AppLayout>
  );
}
