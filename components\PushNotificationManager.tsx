'use client';

import React, { useState, useEffect } from 'react';
import {
  isPushNotificationSupported,
  getNotificationPermission,
  requestNotificationPermission,
  getNotificationPreferences,
  hasNotificationPreference,
  disableNotifications,
  NotificationPermission
} from '@/utils/notificationUtils';

interface PushNotificationManagerProps {
  onPermissionGranted?: () => void;
  onPermissionDenied?: () => void;
  className?: string;
  showAsCard?: boolean;
}

const PushNotificationManager: React.FC<PushNotificationManagerProps> = ({
  onPermissionGranted,
  onPermissionDenied,
  className = '',
  showAsCard = true
}) => {
  const [permission, setPermission] = useState<NotificationPermission>({
    granted: false,
    denied: false,
    default: true
  });
  const [isRequesting, setIsRequesting] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [hasPreference, setHasPreference] = useState(false);

  useEffect(() => {
    // Check support and current permission status
    const supported = isPushNotificationSupported();
    setIsSupported(supported);

    if (supported) {
      const currentPermission = getNotificationPermission();
      setPermission(currentPermission);
      setHasPreference(hasNotificationPreference());
    }
  }, []);

  const handleEnableNotifications = async () => {
    if (!isSupported) {
      alert('Push notifications are not supported in your browser.');
      return;
    }

    setIsRequesting(true);

    try {
      const newPermission = await requestNotificationPermission();
      setPermission(newPermission);
      setHasPreference(true);

      if (newPermission.granted) {
        onPermissionGranted?.();
      } else {
        onPermissionDenied?.();
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      onPermissionDenied?.();
    } finally {
      setIsRequesting(false);
    }
  };

  const handleDisableNotifications = () => {
    disableNotifications();
    setHasPreference(true);
    onPermissionDenied?.();
  };

  // Don't render if notifications are not supported
  if (!isSupported) {
    return null;
  }

  // Don't render if user already has a preference and permission is granted
  if (hasPreference && permission.granted) {
    return null;
  }

  // Don't render if permission is denied
  if (permission.denied) {
    return null;
  }

  const CardContent = () => (
    <div className="text-center">
      <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 mb-4">
        <svg
          className="h-6 w-6 text-indigo-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"
          />
        </svg>
      </div>

      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Get Notified When Ready
      </h3>
      
      <p className="text-sm text-gray-600 mb-6">
        Matrix generation takes about 2 minutes. Enable notifications to get alerted when your content matrix is ready, so you can continue with other tasks.
      </p>

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          onClick={handleEnableNotifications}
          disabled={isRequesting}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${
            isRequesting ? 'cursor-not-allowed' : ''
          }`}
        >
          {isRequesting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Requesting...
            </>
          ) : (
            <>
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
              </svg>
              Enable Notifications
            </>
          )}
        </button>

        <button
          onClick={handleDisableNotifications}
          disabled={isRequesting}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          No Thanks
        </button>
      </div>

      <p className="text-xs text-gray-500 mt-4">
        You can change this preference anytime in your browser settings.
      </p>
    </div>
  );

  if (showAsCard) {
    return (
      <div className={`bg-white rounded-lg shadow-md border border-gray-200 p-6 ${className}`}>
        <CardContent />
      </div>
    );
  }

  return (
    <div className={className}>
      <CardContent />
    </div>
  );
};

export default PushNotificationManager;
