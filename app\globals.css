@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
  max-width: 100%;
  position: relative;
  height: 100%;
}

/* Custom animations */
@layer utilities {
  .animate-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  /* Feature icon hover effects */
  .feature-icon-container {
    transition: all 0.3s ease;
  }

  .feature-icon-container:hover {
    transform: scale(1.1);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4);
  }

  /* Fix for horizontal scrollbar */
  .container-fix {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure all elements respect container boundaries */
  * {
    max-width: 100%;
  }

  /* Mobile menu scrolling */
  .mobile-menu-scroll {
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Loading animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  /* Matrix loading specific animations */
  .matrix-loading-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  .matrix-progress-bar {
    background: linear-gradient(90deg, #4f46e5, #7c3aed, #4f46e5);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
  }

  .notification-cta-glow {
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(79, 70, 229, 0.6);
  }
}
