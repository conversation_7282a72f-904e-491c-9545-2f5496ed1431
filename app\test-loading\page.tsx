'use client';

import { useState } from 'react';
import MatrixLoadingPage from '@/components/MatrixLoadingPage';
import AppLayout from '@/components/AppLayout';

export default function TestLoadingPage() {
  const [showLoading, setShowLoading] = useState(false);

  const createMockWebhookPromise = (): Promise<any> => {
    return new Promise((resolve) => {
      // Simulate 2-minute processing time
      setTimeout(() => {
        resolve({
          _id: 'test-matrix-' + Date.now(),
          success: true,
          message: 'Matrix generated successfully'
        });
      }, 120000); // 2 minutes
    });
  };

  const handleStartTest = () => {
    setShowLoading(true);
  };

  const handleComplete = (matrixId: string) => {
    console.log('Test matrix completed:', matrixId);
    alert(`Matrix generation completed! Matrix ID: ${matrixId}`);
    setShowLoading(false);
  };

  const handleError = (error: string) => {
    console.error('Test matrix error:', error);
    alert(`Matrix generation failed: ${error}`);
    setShowLoading(false);
  };

  if (showLoading) {
    return (
      <MatrixLoadingPage
        keyword="test keyword"
        location="United States"
        language="English"
        projectName="Test Project"
        projectSlug="test-project"
        onComplete={handleComplete}
        onError={handleError}
        webhookPromise={createMockWebhookPromise()}
      />
    );
  }

  return (
    <AppLayout>
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Loading Experience Test Page
          </h1>
          <p className="text-gray-600 mb-6">
            This page allows you to test the matrix loading experience and push notification system.
          </p>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <svg className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-yellow-800">Test Mode</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  This will simulate a 2-minute matrix generation process. You can test the loading animations and push notification features.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">Features to Test:</h2>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>Loading page with progress animation</li>
              <li>Real-time countdown timer</li>
              <li>Progressive status messages</li>
              <li>Push notification permission request</li>
              <li>Notification on completion (if enabled)</li>
              <li>Responsive design on mobile devices</li>
            </ul>
          </div>

          <div className="mt-8">
            <button
              onClick={handleStartTest}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Start Loading Test (2 minutes)
            </button>
          </div>

          <div className="mt-8 bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Testing Tips:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Open browser developer tools to see console logs</li>
              <li>• Try enabling/declining push notifications</li>
              <li>• Test on mobile devices for responsive design</li>
              <li>• Switch tabs to test background notifications</li>
              <li>• Check localStorage for notification preferences</li>
            </ul>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Push Notification Test
          </h2>
          <p className="text-gray-600 mb-4">
            Test push notifications independently of the loading process.
          </p>
          
          <div className="space-y-4">
            <button
              onClick={async () => {
                try {
                  const { requestNotificationPermission } = await import('@/utils/notificationUtils');
                  const permission = await requestNotificationPermission();
                  alert(`Permission result: ${permission.granted ? 'Granted' : 'Denied'}`);
                } catch (error) {
                  alert(`Error: ${error}`);
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Request Notification Permission
            </button>

            <button
              onClick={async () => {
                try {
                  const { sendMatrixCompletionNotification } = await import('@/utils/notificationUtils');
                  sendMatrixCompletionNotification('test keyword', 'Test Project');
                } catch (error) {
                  alert(`Error: ${error}`);
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ml-4"
            >
              Send Test Notification
            </button>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
