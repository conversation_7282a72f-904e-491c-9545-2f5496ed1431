'use client';

import React from 'react';
import NotificationDebugPanel from '@/components/NotificationDebugPanel';

const NotificationDebugPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Notification Debug Center
          </h1>
          <p className="text-gray-600">
            Diagnose and test push notification functionality
          </p>
        </div>
        
        <NotificationDebugPanel />
        
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            This page is for debugging purposes only. Check the browser console for detailed logs.
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotificationDebugPage;
