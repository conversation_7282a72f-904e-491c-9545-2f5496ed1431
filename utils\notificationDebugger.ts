'use client';

/**
 * Notification Debugging Utility
 * Helps diagnose notification issues
 */

import {
  isPushNotificationSupported,
  getNotificationPermission,
  getNotificationPreferences,
  sendLocalNotification
} from './notificationUtils';

export interface NotificationDebugInfo {
  browserSupport: boolean;
  permission: {
    granted: boolean;
    denied: boolean;
    default: boolean;
    raw: string;
  };
  preferences: {
    enabled: boolean;
    matrixGeneration: boolean;
    lastUpdated: string;
  };
  serviceWorker: {
    supported: boolean;
    registered: boolean;
    active: boolean;
  };
  userAgent: string;
  timestamp: string;
}

// Get comprehensive debug information
export const getNotificationDebugInfo = async (): Promise<NotificationDebugInfo> => {
  const debugInfo: NotificationDebugInfo = {
    browserSupport: isPushNotificationSupported(),
    permission: {
      granted: false,
      denied: false,
      default: true,
      raw: 'unknown'
    },
    preferences: {
      enabled: false,
      matrixGeneration: false,
      lastUpdated: ''
    },
    serviceWorker: {
      supported: 'serviceWorker' in navigator,
      registered: false,
      active: false
    },
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    timestamp: new Date().toISOString()
  };

  // Check browser permission
  if (typeof window !== 'undefined' && 'Notification' in window) {
    const permission = getNotificationPermission();
    debugInfo.permission = {
      ...permission,
      raw: Notification.permission
    };
  }

  // Check preferences
  debugInfo.preferences = getNotificationPreferences();

  // Check service worker
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      debugInfo.serviceWorker.registered = !!registration;
      debugInfo.serviceWorker.active = !!(registration?.active);
    } catch (error) {
      console.error('Error checking service worker:', error);
    }
  }

  return debugInfo;
};

// Test notification functionality
export const testNotification = async (): Promise<{
  success: boolean;
  error?: string;
  debugInfo: NotificationDebugInfo;
}> => {
  const debugInfo = await getNotificationDebugInfo();

  try {
    // Check if notifications are supported
    if (!debugInfo.browserSupport) {
      return {
        success: false,
        error: 'Push notifications are not supported in this browser',
        debugInfo
      };
    }

    // Check permission
    if (!debugInfo.permission.granted) {
      return {
        success: false,
        error: `Notification permission not granted. Current status: ${debugInfo.permission.raw}`,
        debugInfo
      };
    }

    // Check preferences
    if (!debugInfo.preferences.enabled || !debugInfo.preferences.matrixGeneration) {
      return {
        success: false,
        error: 'Notification preferences are disabled',
        debugInfo
      };
    }

    // Try to send a test notification
    sendLocalNotification(
      'Test Notification 🔔',
      {
        body: 'This is a test notification to verify the system is working.',
        tag: 'test-notification',
        requireInteraction: false
      }
    );

    return {
      success: true,
      debugInfo
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      debugInfo
    };
  }
};

// Force enable notifications for debugging
export const forceEnableNotifications = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    // Request permission if not granted
    if (Notification.permission !== 'granted') {
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        return {
          success: false,
          error: `Permission denied. Status: ${permission}`
        };
      }
    }

    // Force save preferences
    const preferences = {
      enabled: true,
      matrixGeneration: true,
      lastUpdated: new Date().toISOString()
    };

    localStorage.setItem('notificationPreferences', JSON.stringify(preferences));

    return { success: true };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Log debug information to console
export const logNotificationDebugInfo = async (): Promise<void> => {
  const debugInfo = await getNotificationDebugInfo();
  
  console.group('🔔 Notification Debug Information');
  console.log('Browser Support:', debugInfo.browserSupport);
  console.log('Permission:', debugInfo.permission);
  console.log('Preferences:', debugInfo.preferences);
  console.log('Service Worker:', debugInfo.serviceWorker);
  console.log('User Agent:', debugInfo.userAgent);
  console.log('Timestamp:', debugInfo.timestamp);
  console.groupEnd();

  // Additional checks
  if (!debugInfo.browserSupport) {
    console.warn('❌ Browser does not support push notifications');
  }

  if (!debugInfo.permission.granted) {
    console.warn('❌ Notification permission not granted:', debugInfo.permission.raw);
  }

  if (!debugInfo.preferences.enabled) {
    console.warn('❌ Notification preferences disabled');
  }

  if (!debugInfo.serviceWorker.registered) {
    console.warn('⚠️ Service worker not registered');
  }
};

// Enhanced matrix completion notification with debugging
export const debugSendMatrixCompletionNotification = (keyword: string, projectName: string): void => {
  console.group('🔔 Attempting to send matrix completion notification');
  console.log('Keyword:', keyword);
  console.log('Project:', projectName);

  const preferences = getNotificationPreferences();
  console.log('Preferences:', preferences);

  if (!preferences.enabled) {
    console.warn('❌ Notifications disabled in preferences');
    console.groupEnd();
    return;
  }

  if (!preferences.matrixGeneration) {
    console.warn('❌ Matrix generation notifications disabled');
    console.groupEnd();
    return;
  }

  const permission = getNotificationPermission();
  console.log('Permission:', permission);

  if (!permission.granted) {
    console.warn('❌ Browser permission not granted');
    console.groupEnd();
    return;
  }

  try {
    console.log('✅ Sending notification...');
    sendLocalNotification(
      'Content Matrix Ready! 🎉',
      {
        body: `Your content matrix for "${keyword}" in project "${projectName}" has been generated successfully.`,
        icon: '/favicon.svg',
        tag: 'matrix-completion',
        data: {
          keyword,
          projectName,
          timestamp: Date.now()
        }
      }
    );
    console.log('✅ Notification sent successfully');
  } catch (error) {
    console.error('❌ Error sending notification:', error);
  }

  console.groupEnd();
};
