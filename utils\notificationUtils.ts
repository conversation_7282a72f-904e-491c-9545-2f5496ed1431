'use client';

/**
 * Push Notification Utilities
 * Handles browser push notifications for matrix generation completion
 */

export interface NotificationPermission {
  granted: boolean;
  denied: boolean;
  default: boolean;
}

export interface NotificationPreferences {
  enabled: boolean;
  matrixGeneration: boolean;
  lastUpdated: string;
}

// Check if push notifications are supported
export const isPushNotificationSupported = (): boolean => {
  if (typeof window === 'undefined') return false;
  return 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
};

// Get current notification permission status
export const getNotificationPermission = (): NotificationPermission => {
  if (!isPushNotificationSupported()) {
    return { granted: false, denied: true, default: false };
  }

  const permission = Notification.permission;
  return {
    granted: permission === 'granted',
    denied: permission === 'denied',
    default: permission === 'default'
  };
};

// Request notification permission from user
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (!isPushNotificationSupported()) {
    throw new Error('Push notifications are not supported in this browser');
  }

  try {
    const permission = await Notification.requestPermission();
    const result = {
      granted: permission === 'granted',
      denied: permission === 'denied',
      default: permission === 'default'
    };

    // Store user preference
    if (result.granted) {
      saveNotificationPreferences({
        enabled: true,
        matrixGeneration: true,
        lastUpdated: new Date().toISOString()
      });
    }

    return result;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    throw error;
  }
};

// Save notification preferences to localStorage
export const saveNotificationPreferences = (preferences: NotificationPreferences): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('notificationPreferences', JSON.stringify(preferences));
  } catch (error) {
    console.error('Error saving notification preferences:', error);
  }
};

// Get notification preferences from localStorage
export const getNotificationPreferences = (): NotificationPreferences => {
  if (typeof window === 'undefined') {
    return { enabled: false, matrixGeneration: false, lastUpdated: '' };
  }

  try {
    const stored = localStorage.getItem('notificationPreferences');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Error loading notification preferences:', error);
  }

  return { enabled: false, matrixGeneration: false, lastUpdated: '' };
};

// Send a local notification
export const sendLocalNotification = (title: string, options?: NotificationOptions): void => {
  if (!isPushNotificationSupported()) {
    console.warn('Notifications not supported');
    return;
  }

  const permission = getNotificationPermission();
  if (!permission.granted) {
    console.warn('Notification permission not granted');
    return;
  }

  try {
    const notification = new Notification(title, {
      icon: '/favicon.svg',
      badge: '/favicon.svg',
      tag: 'matrix-generation',
      renotify: true,
      requireInteraction: true,
      ...options
    });

    // Auto-close notification after 10 seconds
    setTimeout(() => {
      notification.close();
    }, 10000);

    // Handle notification click
    notification.onclick = () => {
      window.focus();
      notification.close();
    };

  } catch (error) {
    console.error('Error sending notification:', error);
  }
};

// Send matrix generation completion notification
export const sendMatrixCompletionNotification = (keyword: string, projectName: string): void => {
  const preferences = getNotificationPreferences();
  
  if (!preferences.enabled || !preferences.matrixGeneration) {
    return;
  }

  sendLocalNotification(
    'Content Matrix Ready! 🎉',
    {
      body: `Your content matrix for "${keyword}" in project "${projectName}" has been generated successfully.`,
      icon: '/favicon.svg',
      tag: 'matrix-completion',
      data: {
        keyword,
        projectName,
        timestamp: Date.now()
      }
    }
  );
};

// Register service worker for push notifications (future enhancement)
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if (!isPushNotificationSupported()) {
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js');
    console.log('Service Worker registered:', registration);
    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
};

// Check if user has previously enabled notifications
export const hasNotificationPreference = (): boolean => {
  const preferences = getNotificationPreferences();
  return preferences.lastUpdated !== '';
};

// Disable notifications
export const disableNotifications = (): void => {
  saveNotificationPreferences({
    enabled: false,
    matrixGeneration: false,
    lastUpdated: new Date().toISOString()
  });
};
