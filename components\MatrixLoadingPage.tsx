'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import PushNotificationManager from './PushNotificationManager';
import { LoadingStateManager, LoadingState, MATRIX_GENERATION_STEPS } from '@/utils/loadingStateManager';
import { sendMatrixCompletionNotification, getNotificationPreferences } from '@/utils/notificationUtils';

interface MatrixLoadingPageProps {
  keyword: string;
  location: string;
  language: string;
  projectName: string;
  projectSlug: string;
  onComplete: (matrixId: string) => void;
  onError: (error: string) => void;
  webhookPromise: Promise<any>;
}

const MatrixLoadingPage: React.FC<MatrixLoadingPageProps> = ({
  keyword,
  location,
  language,
  projectName,
  projectSlug,
  onComplete,
  onError,
  webhookPromise
}) => {
  const router = useRouter();
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    currentStep: 0,
    totalSteps: MATRIX_GENERATION_STEPS.length,
    currentMessage: "Preparing to generate content matrix...",
    timeRemaining: 120,
    progress: 0
  });
  const [showNotificationCTA, setShowNotificationCTA] = useState(true);
  const [notificationEnabled, setNotificationEnabled] = useState(false);
  const loadingManagerRef = useRef<LoadingStateManager | null>(null);

  useEffect(() => {
    // Initialize loading manager
    loadingManagerRef.current = new LoadingStateManager(
      MATRIX_GENERATION_STEPS,
      setLoadingState
    );

    // Start loading process
    loadingManagerRef.current.start();

    // Handle webhook completion
    const handleWebhookCompletion = async () => {
      try {
        const result = await webhookPromise;

        // Stop loading manager
        if (loadingManagerRef.current) {
          loadingManagerRef.current.stop();
        }

        // Send notification if enabled
        console.log('🔔 Checking notification status:', { notificationEnabled });
        if (notificationEnabled) {
          console.log('✅ Sending matrix completion notification...');
          sendMatrixCompletionNotification(keyword, projectName);
        } else {
          console.log('❌ Notifications not enabled for this session');
        }

        // Extract matrix ID from result or generate one
        const matrixId = result?.matrixId || result?._id || `${keyword.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;

        // Call completion handler
        onComplete(matrixId);

      } catch (error) {
        console.error('Matrix generation failed:', error);

        // Stop loading manager
        if (loadingManagerRef.current) {
          loadingManagerRef.current.stop();
        }

        onError('Failed to generate matrix. Please try again.');
      }
    };

    handleWebhookCompletion();

    // Cleanup on unmount
    return () => {
      if (loadingManagerRef.current) {
        loadingManagerRef.current.destroy();
      }
    };
  }, [webhookPromise, keyword, projectName, notificationEnabled, onComplete, onError]);

  const handleNotificationPermissionGranted = () => {
    setNotificationEnabled(true);
    setShowNotificationCTA(false);
  };

  const handleNotificationPermissionDenied = () => {
    setNotificationEnabled(false);
    setShowNotificationCTA(false);
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getCurrentStepDescription = (): string => {
    if (loadingManagerRef.current) {
      return loadingManagerRef.current.getCurrentStepDescription();
    }
    return '';
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-indigo-100 mb-4">
            <svg
              className="h-8 w-8 text-indigo-600 animate-pulse"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Generating Content Matrix
          </h2>
          <p className="text-gray-600">
            Creating your SEO content strategy for <span className="font-semibold text-indigo-600">"{keyword}"</span>
          </p>
        </div>

        {/* Progress Section */}
        <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{loadingState.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${loadingState.progress}%` }}
              ></div>
            </div>
          </div>

          {/* Current Step */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600"></div>
              <span className="text-sm font-medium text-gray-900">
                Step {loadingState.currentStep} of {loadingState.totalSteps}
              </span>
            </div>
            <p className="text-gray-700 font-medium mb-1">
              {loadingState.currentMessage}
            </p>
            <p className="text-sm text-gray-500">
              {getCurrentStepDescription()}
            </p>
          </div>

          {/* Time Remaining */}
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-gray-50 rounded-full px-4 py-2">
              <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-gray-700">
                Estimated time remaining: <span className="font-mono font-medium">{formatTime(loadingState.timeRemaining)}</span>
              </span>
            </div>
          </div>

          {/* Project Info */}
          <div className="border-t pt-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Project:</span>
                <p className="font-medium text-gray-900">{projectName}</p>
              </div>
              <div>
                <span className="text-gray-500">Location:</span>
                <p className="font-medium text-gray-900">{location}</p>
              </div>
              <div>
                <span className="text-gray-500">Language:</span>
                <p className="font-medium text-gray-900">{language}</p>
              </div>
              <div>
                <span className="text-gray-500">Keyword:</span>
                <p className="font-medium text-gray-900 truncate">{keyword}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Push Notification CTA */}
        {showNotificationCTA && (
          <PushNotificationManager
            onPermissionGranted={handleNotificationPermissionGranted}
            onPermissionDenied={handleNotificationPermissionDenied}
            className="animate-fade-in"
          />
        )}

        {/* Tips Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <svg className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">
                While you wait...
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Review your content strategy goals</li>
                <li>• Prepare your content creation team</li>
                <li>• Consider additional keywords to research</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Cancel Option */}
        <div className="text-center">
          <button
            onClick={() => router.back()}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            Cancel and go back
          </button>
        </div>
      </div>
    </div>
  );
};

export default MatrixLoadingPage;
