/**
 * Utility functions for processing webhook data
 */

import { KeywordResearchItem, ContentMatrixItem } from '@/types';
import {
  extractKeywordResearchFromWebhook,
  transformWebhookKeywordData
} from './webhookUtils';

/**
 * Process webhook response and extract keyword research data
 * @param webhookResponse The webhook response object
 * @param mainKeyword Main keyword for error messages
 * @param location Location for error messages
 * @param language Language for error messages
 * @returns Object containing keyword research data and source information
 */
export const processKeywordResearchData = (
  webhookResponse: any,
  mainKeyword: string,
  location: string = 'United States',
  language: string = 'English'
): {
  keywordResearch: KeywordResearchItem[];
  dataSource: 'webhook' | 'error';
  message: string;
} => {
  console.log('🔄 Processing keyword research data...');
  console.log('📥 Webhook response:', webhookResponse);

  // Try to extract real data from webhook response
  if (webhookResponse) {
    const extractedData = extractKeywordResearchFromWebhook(webhookResponse);

    if (extractedData && extractedData.length > 0) {
      try {
        const transformedData = transformWebhookKeywordData(extractedData, mainKeyword);

        console.log('✅ Successfully processed webhook keyword research data');
        return {
          keywordResearch: transformedData,
          dataSource: 'webhook',
          message: `Successfully loaded ${transformedData.length} keyword research items from webhook`
        };
      } catch (error) {
        console.error('❌ Error transforming webhook data:', error);
      }
    }
  }

  // No fallback - return empty data with error message
  console.error('❌ No valid keyword research data available from webhook');

  return {
    keywordResearch: [],
    dataSource: 'error',
    message: `No keyword research data available. Webhook response was invalid or empty for keyword: ${mainKeyword}`
  };
};

/**
 * Process content matrix data from webhook response
 * @param webhookResponse The webhook response object
 * @param mainKeyword Main keyword for error messages
 * @param location Location for error messages
 * @param language Language for error messages
 * @returns Object containing content matrix data and source information
 */
export const processContentMatrixData = (
  webhookResponse: any,
  mainKeyword: string,
  location: string = 'United States',
  language: string = 'English'
): {
  contentMatrix: ContentMatrixItem[];
  dataSource: 'webhook' | 'error';
  message: string;
} => {
  console.log('🔄 Processing content matrix data from webhook...');

  // TODO: Implement content matrix extraction from webhook when available
  // For now, return empty data as no mock data is available
  console.log('⚠️ Content matrix extraction not yet implemented');

  return {
    contentMatrix: [],
    dataSource: 'error',
    message: `Content matrix data not available. Webhook extraction not yet implemented for keyword: ${mainKeyword}`
  };
};

/**
 * Process complete webhook response for both keyword research and content matrix
 * @param webhookResponse The webhook response object
 * @param mainKeyword Main keyword for fallback generation
 * @param location Location for fallback generation
 * @param language Language for fallback generation
 * @returns Complete processed data with source information
 */
export const processWebhookData = (
  webhookResponse: any,
  mainKeyword: string,
  location: string = 'United States',
  language: string = 'English'
) => {
  console.log('🚀 Processing complete webhook data...');

  const keywordResult = processKeywordResearchData(webhookResponse, mainKeyword, location, language);
  const contentResult = processContentMatrixData(webhookResponse, mainKeyword, location, language);

  const result = {
    keywordResearch: keywordResult.keywordResearch,
    contentMatrix: contentResult.contentMatrix,
    dataSources: {
      keywordResearch: keywordResult.dataSource,
      contentMatrix: contentResult.dataSource,
    },
    messages: {
      keywordResearch: keywordResult.message,
      contentMatrix: contentResult.message,
    },
    summary: {
      keywordCount: keywordResult.keywordResearch.length,
      contentCount: contentResult.contentMatrix.length,
      hasRealKeywordData: keywordResult.dataSource === 'webhook',
      hasRealContentData: contentResult.dataSource === 'webhook',
      hasErrors: keywordResult.dataSource === 'error' || contentResult.dataSource === 'error',
    }
  };

  console.log('📊 Data processing summary:', result.summary);
  console.log('📝 Messages:', result.messages);

  return result;
};

/**
 * Validate webhook response structure
 * @param response The webhook response to validate
 * @returns Boolean indicating if response has expected structure
 */
export const isValidWebhookResponse = (response: any): boolean => {
  if (!response || typeof response !== 'object') {
    return false;
  }

  // Check for any of the expected response structures
  const hasValidStructure =
    response.keywordResearch ||
    response.data?.keywordResearch ||
    response.webhookResponse?.keywordResearch ||
    response.result?.keywordResearch;

  return Boolean(hasValidStructure);
};
