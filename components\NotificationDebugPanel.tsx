'use client';

import React, { useState, useEffect } from 'react';
import {
  getNotificationDebugInfo,
  testNotification,
  forceEnableNotifications,
  logNotificationDebugInfo,
  debugSendMatrixCompletionNotification,
  NotificationDebugInfo
} from '@/utils/notificationDebugger';

const NotificationDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<NotificationDebugInfo | null>(null);
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadDebugInfo();
  }, []);

  const loadDebugInfo = async () => {
    try {
      const info = await getNotificationDebugInfo();
      setDebugInfo(info);
    } catch (error) {
      console.error('Error loading debug info:', error);
    }
  };

  const handleTestNotification = async () => {
    setIsLoading(true);
    setTestResult('');

    try {
      const result = await testNotification();
      if (result.success) {
        setTestResult('✅ Test notification sent successfully!');
      } else {
        setTestResult(`❌ Test failed: ${result.error}`);
      }
      setDebugInfo(result.debugInfo);
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceEnable = async () => {
    setIsLoading(true);
    setTestResult('');

    try {
      const result = await forceEnableNotifications();
      if (result.success) {
        setTestResult('✅ Notifications force-enabled successfully!');
        await loadDebugInfo();
      } else {
        setTestResult(`❌ Force enable failed: ${result.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogDebugInfo = async () => {
    await logNotificationDebugInfo();
    setTestResult('📝 Debug information logged to console');
  };

  const handleTestMatrixNotification = () => {
    debugSendMatrixCompletionNotification('test keyword', 'Test Project');
    setTestResult('🧪 Matrix completion notification test triggered (check console)');
  };

  const getStatusIcon = (condition: boolean) => {
    return condition ? '✅' : '❌';
  };

  const getStatusColor = (condition: boolean) => {
    return condition ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        🔔 Notification Debug Panel
      </h2>

      {/* Debug Information */}
      {debugInfo && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">System Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Browser Support</h4>
              <p className={`${getStatusColor(debugInfo.browserSupport)}`}>
                {getStatusIcon(debugInfo.browserSupport)} Push Notifications: {debugInfo.browserSupport ? 'Supported' : 'Not Supported'}
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Permission Status</h4>
              <p className={`${getStatusColor(debugInfo.permission.granted)}`}>
                {getStatusIcon(debugInfo.permission.granted)} Permission: {debugInfo.permission.raw}
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">User Preferences</h4>
              <p className={`${getStatusColor(debugInfo.preferences.enabled)}`}>
                {getStatusIcon(debugInfo.preferences.enabled)} Enabled: {debugInfo.preferences.enabled ? 'Yes' : 'No'}
              </p>
              <p className={`${getStatusColor(debugInfo.preferences.matrixGeneration)}`}>
                {getStatusIcon(debugInfo.preferences.matrixGeneration)} Matrix Notifications: {debugInfo.preferences.matrixGeneration ? 'Yes' : 'No'}
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Service Worker</h4>
              <p className={`${getStatusColor(debugInfo.serviceWorker.supported)}`}>
                {getStatusIcon(debugInfo.serviceWorker.supported)} Supported: {debugInfo.serviceWorker.supported ? 'Yes' : 'No'}
              </p>
              <p className={`${getStatusColor(debugInfo.serviceWorker.registered)}`}>
                {getStatusIcon(debugInfo.serviceWorker.registered)} Registered: {debugInfo.serviceWorker.registered ? 'Yes' : 'No'}
              </p>
              <p className={`${getStatusColor(debugInfo.serviceWorker.active)}`}>
                {getStatusIcon(debugInfo.serviceWorker.active)} Active: {debugInfo.serviceWorker.active ? 'Yes' : 'No'}
              </p>
            </div>
          </div>

          <div className="mt-4">
            <h4 className="font-medium mb-2">Browser Information</h4>
            <p className="text-sm text-gray-600 break-all">{debugInfo.userAgent}</p>
          </div>

          <div className="mt-4">
            <h4 className="font-medium mb-2">Last Updated</h4>
            <p className="text-sm text-gray-600">
              {debugInfo.preferences.lastUpdated || 'Never'}
            </p>
          </div>
        </div>
      )}

      {/* Test Result */}
      {testResult && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800">{testResult}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          onClick={loadDebugInfo}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          🔄 Refresh Status
        </button>

        <button
          onClick={handleTestNotification}
          disabled={isLoading}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
        >
          🧪 Test Notification
        </button>

        <button
          onClick={handleForceEnable}
          disabled={isLoading}
          className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
        >
          🔧 Force Enable
        </button>

        <button
          onClick={handleLogDebugInfo}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
        >
          📝 Log to Console
        </button>
      </div>

      <div className="mt-4">
        <button
          onClick={handleTestMatrixNotification}
          disabled={isLoading}
          className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50"
        >
          🎯 Test Matrix Completion Notification
        </button>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-800 mb-2">Troubleshooting Steps:</h3>
        <ol className="list-decimal list-inside text-sm text-yellow-700 space-y-1">
          <li>Check if browser supports notifications (should show ✅)</li>
          <li>Ensure permission is granted (click "Force Enable" if needed)</li>
          <li>Verify preferences are enabled</li>
          <li>Test with "Test Notification" button</li>
          <li>Check browser console for detailed logs</li>
          <li>Try "Test Matrix Completion Notification" to simulate real scenario</li>
        </ol>
      </div>

      {/* Browser-specific Notes */}
      <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-2">Browser-specific Notes:</h3>
        <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
          <li><strong>Chrome:</strong> Notifications work in foreground and background</li>
          <li><strong>Firefox:</strong> May require user interaction before showing notifications</li>
          <li><strong>Safari:</strong> Limited notification support, may not work in all versions</li>
          <li><strong>Edge:</strong> Similar to Chrome, good notification support</li>
          <li><strong>Mobile browsers:</strong> Notification behavior varies significantly</li>
        </ul>
      </div>
    </div>
  );
};

export default NotificationDebugPanel;
